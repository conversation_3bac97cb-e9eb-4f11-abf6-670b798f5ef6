'use client';

import { motion } from 'framer-motion';
import { Target, Eye, Heart, Award, Users, TrendingUp, Globe, Lightbulb } from 'lucide-react';

const About = () => {
  const values = [
    {
      icon: <Lightbulb className="w-8 h-8" />,
      title: '开放',
      description: '拥抱开源技术，构建开放生态'
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: '创新',
      description: '持续技术创新，引领行业发展'
    },
    {
      icon: <Award className="w-8 h-8" />,
      title: '极致',
      description: '追求产品极致，打造用户体验'
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: '共赢',
      description: '与合作伙伴共同成长，实现共赢'
    }
  ];

  const milestones = [
    {
      year: '2020',
      title: '公司成立',
      description: '星擎科技在深圳成立，专注边缘计算领域'
    },
    {
      year: '2021',
      title: '产品发布',
      description: '发布NovaEdge 1.0系列边缘计算设备'
    },
    {
      year: '2022',
      title: '平台上线',
      description: 'NovaBrain AI平台正式上线运营'
    },
    {
      year: '2023',
      title: '规模扩张',
      description: '员工规模突破200人，业务覆盖全国'
    },
    {
      year: '2024',
      title: '国际化',
      description: '启动海外市场布局，进军东南亚'
    },
    {
      year: '2025',
      title: '技术突破',
      description: '自研AI芯片项目启动，技术实力再升级'
    }
  ];

  const achievements = [
    {
      icon: <TrendingUp className="w-12 h-12" />,
      title: '营收增长',
      value: '300%',
      description: '年复合增长率'
    },
    {
      icon: <Users className="w-12 h-12" />,
      title: '团队规模',
      value: '268',
      description: '专业技术人员'
    },
    {
      icon: <Globe className="w-12 h-12" />,
      title: '服务客户',
      value: '500+',
      description: '企业客户'
    },
    {
      icon: <Award className="w-12 h-12" />,
      title: '技术专利',
      value: '50+',
      description: '核心技术专利'
    }
  ];

  return (
    <section id="about" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            关于我们
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            星擎科技成立于2020年，是一家专注于边缘计算和人工智能技术的高科技企业
          </p>
        </motion.div>

        {/* Mission & Vision */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
          <motion.div
            className="bg-white rounded-3xl p-8 shadow-xl"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl mr-4">
                <Target className="w-8 h-8" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900">我们的使命</h3>
            </div>
            <p className="text-lg text-gray-700 leading-relaxed">
              用AI与边缘计算打造万物互联的智能世界，让每一个设备都具备智能感知和决策能力，
              推动产业数字化转型，创造更美好的智能生活。
            </p>
          </motion.div>

          <motion.div
            className="bg-white rounded-3xl p-8 shadow-xl"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4 rounded-2xl mr-4">
                <Eye className="w-8 h-8" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900">我们的愿景</h3>
            </div>
            <p className="text-lg text-gray-700 leading-relaxed">
              成为全球领先的智能边缘计算平台提供商，通过持续的技术创新和产品优化，
              为全球客户提供最优质的边缘智能解决方案。
            </p>
          </motion.div>
        </div>

        {/* Core Values */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              核心价值观
            </h3>
            <p className="text-lg text-gray-600">
              开放 · 创新 · 极致 · 共赢
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="text-blue-600 mb-4 flex justify-center">
                  {value.icon}
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">
                  {value.title}
                </h4>
                <p className="text-gray-600">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Achievements */}
        <motion.div
          className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 lg:p-12 mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-white mb-4">
              发展成就
            </h3>
            <p className="text-lg text-blue-100">
              五年来的快速发展与技术积累
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.title}
                className="text-center"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-white mb-4 flex justify-center">
                  {achievement.icon}
                </div>
                <div className="text-4xl font-bold text-white mb-2">
                  {achievement.value}
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">
                  {achievement.title}
                </h4>
                <p className="text-blue-100 text-sm">
                  {achievement.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              发展历程
            </h3>
            <p className="text-lg text-gray-600">
              从初创到行业领先的发展轨迹
            </p>
          </div>
          
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-600 to-purple-600 rounded-full"></div>
            
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={milestone.year}
                  className={`flex items-center ${
                    index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                  }`}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  viewport={{ once: true }}
                >
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="bg-white rounded-2xl p-6 shadow-lg">
                      <div className="text-2xl font-bold text-blue-600 mb-2">
                        {milestone.year}
                      </div>
                      <h4 className="text-xl font-bold text-gray-900 mb-2">
                        {milestone.title}
                      </h4>
                      <p className="text-gray-600">
                        {milestone.description}
                      </p>
                    </div>
                  </div>
                  
                  <div className="w-6 h-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full border-4 border-white shadow-lg z-10"></div>
                  
                  <div className="w-1/2"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
