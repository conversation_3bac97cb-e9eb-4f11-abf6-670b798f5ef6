'use client';

import { motion } from 'framer-motion';
import { Brain, Database, Monitor, Code, Layers, Cpu, Network, Shield } from 'lucide-react';

const Technology = () => {
  const platforms = [
    {
      icon: <Brain className="w-16 h-16" />,
      title: 'NovaBrain™ AI平台',
      description: '支持图像/视频数据分析的智能算法平台',
      features: [
        '图像/视频数据分析',
        '自研神经网络模型压缩算法（NovaPrune）',
        '边缘设备低功耗运行',
        '多模态数据处理',
        '实时推理引擎'
      ],
      gradient: 'from-purple-600 to-blue-600',
      bgGradient: 'from-purple-50 to-blue-50'
    },
    {
      icon: <Database className="w-16 h-16" />,
      title: 'NovaMesh™ 数据中台',
      description: '实时数据采集、同步、清洗、聚合的统一平台',
      features: [
        '实时数据采集/同步/清洗/聚合',
        '多协议兼容（MQTT、Modbus、OPC UA）',
        '分布式数据存储',
        '数据质量监控',
        '智能数据治理'
      ],
      gradient: 'from-green-600 to-teal-600',
      bgGradient: 'from-green-50 to-teal-50'
    },
    {
      icon: <Monitor className="w-16 h-16" />,
      title: 'NovaOS 边缘计算系统',
      description: 'Linux内核微定制的稳定边缘计算操作系统',
      features: [
        'Linux内核微定制，稳定运行2年+',
        '模块热插拔支持',
        '远程配置管理',
        '权限分级管理',
        '容器化部署'
      ],
      gradient: 'from-orange-600 to-red-600',
      bgGradient: 'from-orange-50 to-red-50'
    }
  ];

  const techSpecs = [
    {
      icon: <Cpu className="w-8 h-8" />,
      title: '高性能计算',
      value: '1000+',
      unit: 'TOPS',
      description: 'AI算力支持'
    },
    {
      icon: <Network className="w-8 h-8" />,
      title: '低延迟处理',
      value: '<10',
      unit: 'ms',
      description: '边缘推理延迟'
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: '系统稳定性',
      value: '99.9',
      unit: '%',
      description: '服务可用性'
    },
    {
      icon: <Layers className="w-8 h-8" />,
      title: '并发处理',
      value: '10000+',
      unit: '路',
      description: '视频流处理'
    }
  ];

  const architectureLayers = [
    {
      name: '应用层',
      description: '智慧工业、智慧城市应用',
      color: 'bg-blue-500'
    },
    {
      name: '平台层',
      description: 'NovaBrain AI平台、NovaMesh数据中台',
      color: 'bg-purple-500'
    },
    {
      name: '系统层',
      description: 'NovaOS边缘计算系统',
      color: 'bg-green-500'
    },
    {
      name: '硬件层',
      description: 'NovaEdge边缘计算设备',
      color: 'bg-orange-500'
    }
  ];

  return (
    <section id="technology" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            技术平台
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            基于自主研发的核心技术平台，构建完整的边缘智能计算生态系统
          </p>
        </motion.div>

        {/* Technology Platforms */}
        <div className="space-y-12 mb-20">
          {platforms.map((platform, index) => (
            <motion.div
              key={platform.title}
              className={`bg-gradient-to-r ${platform.bgGradient} rounded-3xl p-8 lg:p-12`}
              initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <div className={`flex flex-col lg:flex-row items-center gap-8 ${
                index % 2 === 1 ? 'lg:flex-row-reverse' : ''
              }`}>
                <div className="flex-shrink-0">
                  <div className={`bg-gradient-to-r ${platform.gradient} text-white p-6 rounded-3xl shadow-2xl`}>
                    {platform.icon}
                  </div>
                </div>
                
                <div className="flex-1 text-center lg:text-left">
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    {platform.title}
                  </h3>
                  <p className="text-lg text-gray-700 mb-6">
                    {platform.description}
                  </p>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {platform.features.map((feature, featureIndex) => (
                      <motion.div
                        key={featureIndex}
                        className="flex items-center space-x-3"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.2 + featureIndex * 0.1 }}
                        viewport={{ once: true }}
                      >
                        <div className={`w-3 h-3 bg-gradient-to-r ${platform.gradient} rounded-full`}></div>
                        <span className="text-gray-700">{feature}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Technical Specifications */}
        <motion.div
          className="bg-gray-900 rounded-3xl p-8 lg:p-12 mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-white mb-4">
              技术指标
            </h3>
            <p className="text-lg text-gray-300">
              行业领先的技术性能指标
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {techSpecs.map((spec, index) => (
              <motion.div
                key={spec.title}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-blue-400 mb-4 flex justify-center">
                  {spec.icon}
                </div>
                <div className="text-4xl font-bold text-white mb-2">
                  {spec.value}
                  <span className="text-2xl text-blue-400">{spec.unit}</span>
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">
                  {spec.title}
                </h4>
                <p className="text-gray-400 text-sm">
                  {spec.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Architecture Diagram */}
        <motion.div
          className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-3xl p-8 lg:p-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              技术架构
            </h3>
            <p className="text-lg text-gray-600">
              分层架构设计，确保系统的可扩展性和稳定性
            </p>
          </div>
          
          <div className="max-w-2xl mx-auto">
            {architectureLayers.map((layer, index) => (
              <motion.div
                key={layer.name}
                className="mb-4 last:mb-0"
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <div className={`${layer.color} text-white rounded-2xl p-6 shadow-lg`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-xl font-bold mb-2">{layer.name}</h4>
                      <p className="text-white/90">{layer.description}</p>
                    </div>
                    <Code className="w-8 h-8 opacity-80" />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Technology;
