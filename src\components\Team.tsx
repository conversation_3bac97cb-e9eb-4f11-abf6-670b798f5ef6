'use client';

import { motion } from 'framer-motion';
import { Linkedin, Mail, Award, GraduationCap, Building, Users } from 'lucide-react';

const Team = () => {
  const founders = [
    {
      name: '胡耀麟',
      position: '联合创始人 & CEO',
      background: '前腾讯云边缘计算负责人，清华大学硕士，专长智能平台架构',
      education: '清华大学硕士',
      experience: '腾讯云边缘计算负责人',
      expertise: '智能平台架构',
      avatar: '/api/placeholder/200/200',
      gradient: 'from-blue-600 to-cyan-600'
    },
    {
      name: '冯潇然',
      position: 'CTO',
      background: '计算机视觉博士，曾任谷歌AI研究员、旷视科技高级算法总监',
      education: '计算机视觉博士',
      experience: '谷歌AI研究员、旷视科技高级算法总监',
      expertise: 'AI算法与计算机视觉',
      avatar: '/api/placeholder/200/200',
      gradient: 'from-purple-600 to-pink-600'
    },
    {
      name: '陈慧娟',
      position: 'COO',
      background: '10年运营管理经验，前华为智能终端事业部高管',
      education: '工商管理硕士',
      experience: '华为智能终端事业部高管',
      expertise: '运营管理与商业拓展',
      avatar: '/api/placeholder/200/200',
      gradient: 'from-green-600 to-emerald-600'
    },
    {
      name: '王志恒',
      position: 'CFO',
      background: '财务专家，拥有德勤与软银中国基金投融资背景',
      education: 'CPA注册会计师',
      experience: '德勤、软银中国基金',
      expertise: '财务管理与投融资',
      avatar: '/api/placeholder/200/200',
      gradient: 'from-orange-600 to-red-600'
    }
  ];

  const departments = [
    {
      name: 'AI研究院',
      count: 52,
      description: '专注于前沿AI算法研究与产品化',
      icon: <Award className="w-8 h-8" />,
      color: 'bg-blue-500'
    },
    {
      name: '嵌入式系统组',
      count: 41,
      description: '边缘计算硬件与系统开发',
      icon: <Building className="w-8 h-8" />,
      color: 'bg-purple-500'
    },
    {
      name: '云平台团队',
      count: 37,
      description: '云端平台架构与服务开发',
      icon: <Users className="w-8 h-8" />,
      color: 'bg-green-500'
    },
    {
      name: '工程交付部',
      count: 26,
      description: '项目实施与客户服务',
      icon: <Building className="w-8 h-8" />,
      color: 'bg-orange-500'
    }
  ];

  const culture = [
    {
      title: '弹性办公',
      description: '支持远程协作，灵活工作时间',
      icon: '🏠'
    },
    {
      title: '技术驱动',
      description: '追求技术极致，结果导向',
      icon: '🚀'
    },
    {
      title: '股权激励',
      description: '与公司共同成长，分享发展红利',
      icon: '💎'
    },
    {
      title: '持续学习',
      description: '星擎学院，与清华/中科院联合培训',
      icon: '📚'
    }
  ];

  return (
    <section id="team" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            团队介绍
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            汇聚行业顶尖人才，打造世界级的技术团队
          </p>
        </motion.div>

        {/* Founders */}
        <div className="mb-20">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              创始团队
            </h3>
            <p className="text-lg text-gray-600">
              来自顶级科技公司的资深专家
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {founders.map((founder, index) => (
              <motion.div
                key={founder.name}
                className="bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
              >
                <div className="flex items-start space-x-6">
                  <div className={`w-20 h-20 bg-gradient-to-r ${founder.gradient} rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg`}>
                    {founder.name.charAt(0)}
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="text-2xl font-bold text-gray-900 mb-2">
                      {founder.name}
                    </h4>
                    <p className="text-lg text-blue-600 font-semibold mb-4">
                      {founder.position}
                    </p>
                    <p className="text-gray-700 mb-6 leading-relaxed">
                      {founder.background}
                    </p>
                    
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <GraduationCap className="w-5 h-5 text-gray-500" />
                        <span className="text-gray-700">{founder.education}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Building className="w-5 h-5 text-gray-500" />
                        <span className="text-gray-700">{founder.experience}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Award className="w-5 h-5 text-gray-500" />
                        <span className="text-gray-700">{founder.expertise}</span>
                      </div>
                    </div>
                    
                    <div className="flex space-x-4 mt-6">
                      <button className="p-2 bg-gray-100 hover:bg-blue-100 rounded-lg transition-colors duration-200">
                        <Linkedin className="w-5 h-5 text-gray-600 hover:text-blue-600" />
                      </button>
                      <button className="p-2 bg-gray-100 hover:bg-blue-100 rounded-lg transition-colors duration-200">
                        <Mail className="w-5 h-5 text-gray-600 hover:text-blue-600" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Organization Structure */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              组织架构
            </h3>
            <p className="text-lg text-gray-600">
              268名专业人员，构建高效协作团队
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {departments.map((dept, index) => (
              <motion.div
                key={dept.name}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className={`${dept.color} text-white p-3 rounded-xl w-fit mb-4`}>
                  {dept.icon}
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-2">
                  {dept.name}
                </h4>
                <div className="text-3xl font-bold text-blue-600 mb-3">
                  {dept.count}人
                </div>
                <p className="text-gray-600 text-sm">
                  {dept.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Company Culture */}
        <motion.div
          className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8 lg:p-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              公司文化
            </h3>
            <p className="text-lg text-gray-600">
              营造开放包容的工作环境，激发团队创新活力
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {culture.map((item, index) => (
              <motion.div
                key={item.title}
                className="text-center"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-6xl mb-4">{item.icon}</div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">
                  {item.title}
                </h4>
                <p className="text-gray-600">
                  {item.description}
                </p>
              </motion.div>
            ))}
          </div>
          
          <motion.div
            className="mt-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="bg-white rounded-2xl p-8 shadow-lg inline-block">
              <h4 className="text-2xl font-bold text-gray-900 mb-4">
                加入我们
              </h4>
              <p className="text-gray-600 mb-6">
                与优秀的人一起，做有意义的事
              </p>
              <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-300">
                查看职位
              </button>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Team;
