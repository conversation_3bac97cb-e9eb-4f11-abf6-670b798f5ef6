'use client';

import { motion } from 'framer-motion';
import { MapPin, Phone, Mail, Globe, Linkedin, Twitter, Github, ArrowUp } from 'lucide-react';

const Footer = () => {
  const quickLinks = [
    { name: '首页', href: '#home' },
    { name: '产品与服务', href: '#products' },
    { name: '技术平台', href: '#technology' },
    { name: '关于我们', href: '#about' },
    { name: '团队介绍', href: '#team' },
    { name: '联系我们', href: '#contact' }
  ];

  const products = [
    { name: 'NovaEdge™ 边缘计算设备', href: '#novaedge' },
    { name: 'NovaBrain™ AI算法平台', href: '#novabrain' },
    { name: '工业智能解决方案', href: '#industrial' },
    { name: '智慧城市系统', href: '#smart-city' }
  ];

  const company = [
    { name: '公司简介', href: '#about' },
    { name: '发展历程', href: '#about' },
    { name: '核心团队', href: '#team' },
    { name: '企业文化', href: '#team' },
    { name: '人才招聘', href: '#' },
    { name: '投资者关系', href: '#' }
  ];

  const support = [
    { name: '技术文档', href: '#' },
    { name: '开发者中心', href: '#' },
    { name: 'API参考', href: '#' },
    { name: '常见问题', href: '#' },
    { name: '技术支持', href: '#contact' },
    { name: '培训服务', href: '#' }
  ];

  const socialLinks = [
    { icon: <Linkedin className="w-5 h-5" />, href: '#', name: 'LinkedIn' },
    { icon: <Twitter className="w-5 h-5" />, href: '#', name: 'Twitter' },
    { icon: <Github className="w-5 h-5" />, href: '#', name: 'GitHub' }
  ];

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Company Info */}
            <motion.div
              className="lg:col-span-1"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">星</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold">星擎科技</h3>
                  <p className="text-gray-400 text-sm">NovaDrive Technologies</p>
                </div>
              </div>
              
              <p className="text-gray-300 mb-6 leading-relaxed">
                用AI与边缘计算打造万物互联的智能世界，成为全球领先的智能边缘计算平台提供商。
              </p>
              
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <MapPin className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-300 text-sm">深圳市南山区科技园深港创智中心A座</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-300 text-sm">+86 0755-XXXX-XXXX</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-300 text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3">
                  <Globe className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-300 text-sm">www.novadrive.cn</span>
                </div>
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold mb-6">快速导航</h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors duration-200"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Products */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold mb-6">产品服务</h4>
              <ul className="space-y-3">
                {products.map((product) => (
                  <li key={product.name}>
                    <a
                      href={product.href}
                      className="text-gray-300 hover:text-white transition-colors duration-200"
                    >
                      {product.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Support */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold mb-6">技术支持</h4>
              <ul className="space-y-3">
                {support.map((item) => (
                  <li key={item.name}>
                    <a
                      href={item.href}
                      className="text-gray-300 hover:text-white transition-colors duration-200"
                    >
                      {item.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-gray-800 py-8">
          <div className="flex flex-col lg:flex-row items-center justify-between">
            <motion.div
              className="text-center lg:text-left mb-4 lg:mb-0"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <p className="text-gray-400 text-sm">
                © 2025 星擎科技有限公司. 保留所有权利. | 
                <a href="#" className="hover:text-white transition-colors duration-200 ml-1">隐私政策</a> | 
                <a href="#" className="hover:text-white transition-colors duration-200 ml-1">服务条款</a> | 
                <a href="#" className="hover:text-white transition-colors duration-200 ml-1">法律声明</a>
              </p>
              <p className="text-gray-500 text-xs mt-1">
                粤ICP备XXXXXXXX号 | 粤公网安备XXXXXXXXXXXXXX号
              </p>
            </motion.div>

            <motion.div
              className="flex items-center space-x-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              {/* Social Links */}
              <div className="flex items-center space-x-4">
                {socialLinks.map((social) => (
                  <a
                    key={social.name}
                    href={social.href}
                    className="text-gray-400 hover:text-white transition-colors duration-200"
                    aria-label={social.name}
                  >
                    {social.icon}
                  </a>
                ))}
              </div>

              {/* Back to Top */}
              <button
                onClick={scrollToTop}
                className="bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white p-2 rounded-lg transition-all duration-200"
                aria-label="回到顶部"
              >
                <ArrowUp className="w-5 h-5" />
              </button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Partnership Logos */}
      <motion.div
        className="border-t border-gray-800 py-8 bg-gray-800/50"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-6">
            <h4 className="text-lg font-semibold text-gray-300">战略合作伙伴</h4>
          </div>
          <div className="flex flex-wrap items-center justify-center gap-8 opacity-60">
            {['华为云', '腾讯云', 'NVIDIA', '阿里云IoT', '清华大学', '香港科技大学'].map((partner) => (
              <div
                key={partner}
                className="bg-gray-700 text-gray-300 px-6 py-3 rounded-lg text-sm font-medium"
              >
                {partner}
              </div>
            ))}
          </div>
        </div>
      </motion.div>
    </footer>
  );
};

export default Footer;
