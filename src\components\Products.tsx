'use client';

import { motion } from 'framer-motion';
import { Cpu, Brain, Factory, Building2, Shield, Zap, Globe, Settings } from 'lucide-react';

const Products = () => {
  const products = [
    {
      id: 'novaedge',
      icon: <Cpu className="w-12 h-12" />,
      title: 'NovaEdge™ 边缘计算设备',
      subtitle: 'AI工业终端系列',
      description: '适用于工厂、城市、交通等环境的智能边缘计算设备',
      features: [
        '搭载NVIDIA Jetson/华为昇腾模块',
        '支持本地推理、断点续算',
        '远程OTA更新',
        'IP65防护等级',
        '模块化设计'
      ],
      gradient: 'from-blue-500 to-cyan-500',
      bgColor: 'bg-blue-50'
    },
    {
      id: 'novabrain',
      icon: <Brain className="w-12 h-12" />,
      title: 'NovaBrain™ AI算法平台',
      subtitle: '智能分析云平台',
      description: '提供图像识别、语义分割、行为分析等多任务AI模型',
      features: [
        '图像识别与语义分割',
        '行为分析算法',
        'SDK/API服务',
        '训练平台支持',
        '私有化部署'
      ],
      gradient: 'from-purple-500 to-pink-500',
      bgColor: 'bg-purple-50'
    },
    {
      id: 'industrial',
      icon: <Factory className="w-12 h-12" />,
      title: '工业智能解决方案',
      subtitle: '智能制造系统',
      description: '质检自动化、AGV路径优化、MES集成的完整解决方案',
      features: [
        '质检自动化系统',
        'AGV路径优化',
        'MES系统集成',
        '多模态AI视觉检测',
        '生产数据分析'
      ],
      gradient: 'from-green-500 to-emerald-500',
      bgColor: 'bg-green-50'
    },
    {
      id: 'smart-city',
      icon: <Building2 className="w-12 h-12" />,
      title: '智慧城市系统',
      subtitle: '城市大脑平台',
      description: '城市交通AI流控、智能停车、环境监测一体化平台',
      features: [
        '交通AI流量控制',
        '智能停车管理',
        '环境监测系统',
        '城市大脑集成',
        '实时数据分析'
      ],
      gradient: 'from-orange-500 to-red-500',
      bgColor: 'bg-orange-50'
    }
  ];

  const techHighlights = [
    {
      icon: <Shield className="w-8 h-8" />,
      title: '安全可靠',
      description: '企业级安全保障'
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: '高性能',
      description: '毫秒级响应速度'
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: '全球部署',
      description: '支持多地域部署'
    },
    {
      icon: <Settings className="w-8 h-8" />,
      title: '易于集成',
      description: '标准API接口'
    }
  ];

  return (
    <section id="products" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            产品与服务
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            从边缘计算设备到AI算法平台，从工业智能到智慧城市，
            我们提供完整的智能化解决方案
          </p>
        </motion.div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              className={`${product.bgColor} rounded-3xl p-8 hover:shadow-2xl transition-all duration-500`}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
            >
              <div className="flex items-start space-x-6">
                <div className={`bg-gradient-to-r ${product.gradient} text-white p-4 rounded-2xl shadow-lg`}>
                  {product.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {product.title}
                  </h3>
                  <p className="text-lg text-gray-600 mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-gray-700 mb-6">
                    {product.description}
                  </p>
                  
                  <div className="space-y-3">
                    {product.features.map((feature, featureIndex) => (
                      <motion.div
                        key={featureIndex}
                        className="flex items-center space-x-3"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.2 + featureIndex * 0.1 }}
                        viewport={{ once: true }}
                      >
                        <div className={`w-2 h-2 bg-gradient-to-r ${product.gradient} rounded-full`}></div>
                        <span className="text-gray-700">{feature}</span>
                      </motion.div>
                    ))}
                  </div>
                  
                  <motion.button
                    className={`mt-6 bg-gradient-to-r ${product.gradient} text-white px-6 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-300`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    了解更多
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Tech Highlights */}
        <motion.div
          className="bg-white rounded-3xl p-8 lg:p-12 shadow-xl"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              技术优势
            </h3>
            <p className="text-lg text-gray-600">
              基于前沿技术，为客户提供稳定可靠的智能化服务
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {techHighlights.map((highlight, index) => (
              <motion.div
                key={highlight.title}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-blue-600 mb-4 flex justify-center">
                  {highlight.icon}
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-2">
                  {highlight.title}
                </h4>
                <p className="text-gray-600">
                  {highlight.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Products;
